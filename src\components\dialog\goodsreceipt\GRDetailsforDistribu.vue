<template>
  <q-dialog v-model="isOpen" maximized>
    <q-card style="max-width: 1100px"><q-card-section>
        <div class="row items-center justify-between">
          <div class="text-h6 text-weight-bold">รายละเอียดใบรับสินค้า</div>
          <q-btn icon="close" @click="closeDialog()" flat rounded />
        </div>
      </q-card-section><q-separator />
      <q-card-section :style="{ overflow: 'auto', maxHeight: '80vh' }">
        <template v-if="store.form">
          <div class="col-lg-12 row">
            <div class="col-12 col-md-4 q-mt-sm order-1 order-md-1">
              <div class="gap-container">
                <div class="text-white shadow-2 container-headerhalf full-width row items-center">
                  รหัสใบ Goods-Receipt
                </div>
                <div class="shadow-2 containerhalf full-width">
                  <div v-if="store.form" class="text-po">
                    {{ store.form.code }}
                  </div>
                </div>
              </div>
            </div>
            <div class="col-12 col-md-8 q-mt-sm order-2 order-md-1">
              <div class="gap-container">
                <div class="gap-container-left">
                  <div class="text-white shadow-2 container-headerhalf2 full-width row items-center">
                    <span>เปลี่ยนสถานะสินค้า</span>
                    <span class="status-text">สถานะสินค้า : {{ store.form.status }}</span>
                  </div>
                  <div class="shadow-2 containerhalf2 flex-container full-width">
                    <div class="row q-col-gutter-sm" style="font-size: 16px">
                      <q-radio v-model="store.form.status" val="เตรียมรายการ" label="เตรียมรายการ" color="orange"
                        class="custom-radio" size="lg" />
                      <q-radio v-model="store.form.status" val="ดำเนินการ" label="ดำเนินการ" color="blue"
                        class="custom-radio" />
                      <q-radio v-model="store.form.status" val="เสร็จสมบูรณ์" label="เสร็จสมบูรณ์" color="green"
                        class="custom-radio" />
                      <q-radio v-model="store.form.status" val="ยกเลิก" label="ยกเลิก" color="red"
                        class="custom-radio" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="gap-container">
            <div class="text-white shadow-2 container-header row items-center">
              รายละเอียดบริษัทจำหน่ายสินค้า
              <q-btn v-if="store.form.status === 'เตรียมรายการ'" class="btn-add" label="เปลี่ยนบริษัทจำหน่าย"
                @click="openChangeDistributorDialog()" style="margin-right: 20px"></q-btn>

            </div>
            <div class="shadow-2 container">
              <div class="row q-col-gutter-md" style="margin-left: 20px">
                <div class="col-12 col-md-1 q-mt-md" style="margin-left: 20px">รหัสบริษัท</div>
                <div class="col-12 col-md-4">
                  <q-input class="input-container" dense borderless
                    :model-value="store.form?.distributor?.supplier_number || ''"
                    @update:model-value="(val) => store.form && store.form.distributor && (store.form.distributor.supplier_number = String(val))"
                    type="text" />
                </div>

                <div class="col-12 col-md-1 q-mt-md" style="margin-left: 20px">ชื่อผู้ติดต่อ</div>
                <div class="col-12 col-md-4">
                  <q-input class="input-container" :model-value="store.form?.distributor?.contact_name || ''"
                    @update:model-value="(val) => store.form && store.form.distributor && (store.form.distributor.contact_name = String(val))"
                    dense borderless type="text" style="width: 125%" />
                </div>
              </div>

              <div class="row q-col-gutter-md" style="margin-top: 10px; margin-left: 20px">
                <div class="col-12 col-md-1 q-mt-md" style="margin-left: 20px">ชื่อบริษัท</div>
                <div class="col-12 col-md-4">
                  <q-input class="input-container" :model-value="store.form?.distributor?.name || ''"
                    @update:model-value="(val) => store.form && store.form.distributor && (store.form.distributor.name = String(val))"
                    dense borderless type="text" style="width: 100%">
                    <template #append></template>
                  </q-input>
                </div>

                <div class="col-12 col-md-1 q-mt-md" style="margin-left: 20px">ที่อยู่</div>
                <div class="col-12 col-md-5">
                  <q-input class="input-container" :model-value="store.form?.distributor?.address || ''"
                    @update:model-value="(val) => store.form && store.form.distributor && (store.form.distributor.address = String(val))"
                    dense borderless type="textarea" />
                </div>
              </div>
            </div>
          </div>

          <div class="gap-container">
            <div class="text-white shadow-2 container-header row items-center">
              รายละเอียดการรับสินค้า
              <q-btn v-if="store.form.status === 'เตรียมรายการ'" class="btn-add" label="แก้ไข"
                style="color: red"></q-btn>
            </div>
            <div class="shadow-2 container">
              <div class="row justify-between">
                <div class="col-11 row q-my-sm">
                  <div class="col-1 q-mt-md q-pr-md" style="margin-left: 10px">เลขที่</div>
                  <q-input dense borderless class="input-container" v-model="store.form.id"
                    style="width: 175px; height: 40px; margin-left: 22px" readonly />
                  <div class="col-2 q-pr-md flex flex-center" style="margin-left: 30px">สถานะ</div>
                  <div class="row q-col-gutter-sm" style="margin-left: 5px">
                    <q-radio keep-color v-model="store.form.status" val="เตรียมรายการ" label="เตรียมรายการ"
                      color="orange" style="color: orange" size="sm" disable />
                    <q-radio keep-color v-model="store.form.status" val="ดำเนินการ" label="ดำเนินการ" color="blue"
                      style="color: royalblue" size="sm" disable />
                    <q-radio keep-color v-model="store.form.status" val="เสร็จสมบูรณ์" label="เสร็จสมบูรณ์"
                      color="green" style="color: green" size="sm" disable />
                    <q-radio keep-color v-model="store.form.status" val="ยกเลิก" label="ยกเลิก" color="red"
                      style="color: red" size="sm" disable />
                  </div>
                </div>
                <div class="col-3 width-column" style="margin-left: 10px">
                  <div class="row items-center q-mb-sm" style="margin-top: 10px" justify-content: space-between>
                    <div class="col-4 q-mt-sm q-pr-md">วันที่เอกสาร</div>
                    <q-input dense borderless class="input-container col-7" v-model="formattedDate" readonly />

                    <div class="col-12 row q-col-gutter-ms" style="margin-top: 20px">
                      <div class="col-4 q-mt-sm q-pr-md">รวมเงิน</div>
                      <q-input number dense borderless class="input-container col-7" v-model="totalNet" readonly
                        type="number" style="height: 40px" />
                    </div>
                    <div class="col-12 row" style="margin-top: 20px">
                      <div class="col-4 q-mt-sm q-pr-md">มูลค่าภาษี</div>
                      <q-input number dense borderless class="input-container col-7" v-model="tax" type="number"
                        readonly />
                    </div>
                    <div class="col-12 row" style="margin-top: 20px">
                      <div class="col-4 q-mt-sm q-pr-md">รวมเงินชำระ</div>
                      <q-input number dense borderless class="input-container col-7" v-model="grTotal" readonly
                        type="number" />
                    </div>
                  </div>
                </div>
                <div class="col-3 width-column">
                  <div class="col-12 row q-col-gutter-ms" style="margin-top: 20px">
                    <div class="col-5 q-mt-sm">พนักงาน</div>
                    <q-input class="input-container col-7" :model-value="store.form?.user?.name || ''" dense borderless
                      readonly />
                  </div>
                  <div class="col-12 row q-col-gutter-ms" style="margin-top: 20px">
                    <div class="col-5 q-mt-sm">วันที่รับ</div>
                    <q-input dense borderless class="input-container col-7" v-model="formattedReceiveDate" readonly />
                  </div>
                  <div class="col-12 row" style="margin-top: 20px">
                    <div class="col-5 q-mt-sm q-pr-md">เลขที่ใบสั่งซื้อ</div>
                    <q-input number dense borderless class="input-container col-7" v-model="store.form.po_code" />
                  </div>
                  <div class="col-12 row" style="margin-top: 20px">
                    <div class="col-5 q-mt-sm q-pr-md">เลขที่ใบกำกับภาษี</div>
                    <q-input number dense borderless class="input-container col-7"
                      v-model="store.form.tax_invoice_number" />
                  </div>
                  <div class="col-12 row" style="margin-top: 20px"> <!--ma edit-->
                    <div class="col-5 q-mt-sm q-pr-md">วันที่ใบกำกับภาษี</div>

                    <q-input dense borderless class="input-container" v-model="formattedInvoiceDate" style="width: 58%">
                      <q-icon name="event" size="md" color="black" style="cursor: pointer; margin-top: 5px">
                        <q-popup-proxy transition-show="scale" transition-hide="scale">
                          <q-date v-model="formattedInvoiceDate" mask="DD/MM/YYYY" color="teal" />
                        </q-popup-proxy>
                      </q-icon>
                    </q-input>
                  </div>
                  <div class="col-12 row" style="margin-top: 20px">
                    <div class="col-5 q-mt-sm q-pr-md">วันที่ให้เครดิต</div>

                    <q-input dense borderless class="input-container" v-model="formattedCreditDate" style="width: 58%">
                      <q-icon name="event" size="md" color="black" style="cursor: pointer; margin-top: 5px">
                        <q-popup-proxy transition-show="scale" transition-hide="scale">
                          <q-date v-model="formattedCreditDate" mask="DD/MM/YYYY" color="teal" />
                        </q-popup-proxy>
                      </q-icon>
                    </q-input>
                  </div>
                  <div class="col-12 row" style="margin-top: 20px">
                    <div class="col-5 q-mt-sm q-pr-md">จำนวนวันเครดิต</div>
                    <q-input number dense borderless class="input-container col-7" v-model="store.form.credit_days" />
                  </div>
                  <div class="col-12 row" style="margin-top: 20px">
                    <div class="col-5 q-mt-sm q-pr-md">วันที่สิ้นสุดเครดิต</div>
                    <q-input dense borderless class="input-container col-7" v-model="formattedCreditDueDate" readonly />
                  </div>
                </div>
                <div class="col-3 width-column">
                  <div class="col-12 row justify-center" style="margin-top: 20px">
                    <q-checkbox class="mini-container-header" v-model="store.form.is_tax_invoice"
                      label="ใบรับสินค้ามีภาษี" color="blue-grey-9" size="xs" />
                    <q-slide-transition>
                      <div v-if="store.form.product_price_tax" class="mini-container" style="font-size: 13px">
                        <!-- รายละเอียดภาษี -->
                        <div class="row q-mb-sm">
                          <div class="col-6" style="margin-top: 15px; margin-left: 5px">
                            ภาษีมูลค่าเพิ่ม (%)
                          </div>
                          <q-input v-model="store.form.vat_percent" dense borderless class="input-container-v3" />
                        </div>
                        <div class="col-6" style="margin-left: 5px">ราคารายการสินค้า</div>
                        <q-option-group v-model="store.form.product_price_tax" :options="[
                          { label: 'รวมภาษี', value: 'รวมภาษี' },
                          { label: 'ไม่รวมภาษี', value: 'ไม่รวมภาษี' },
                        ]" type="radio" color="grey-8" inline size="xs" />
                      </div>
                    </q-slide-transition>
                  </div>
                  <div class="col-12 row justify-center" style="margin-top: 20px">
                    <q-checkbox class="mini-container-header" v-model="store.form.is_manual_discount_before_tax"
                      label="ส่วนลดท้ายบิล ก่อนภาษี" color="blue-grey-9" size="xs" />
                    <q-slide-transition>
                      <div v-if="store.form.is_manual_discount_before_tax" class="mini-container">
                        <!-- รายละเอียดภาษี -->
                        <div class="row q-mb-sm items-center" style="font-size: 13px; margin-top: 10px">
                          <div class="col-auto" style="margin-left: 10px">ส่วนลด</div>
                          <div class="col-auto">
                            <q-option-group v-model="store.form.order_discount_tax" :options="[
                              { label: 'รวมภาษี', value: 'รวมภาษี' },
                              { label: 'ไม่รวมภาษี', value: 'ไม่รวมภาษี' },
                            ]" type="radio" color="grey-8" size="xs" inline />
                          </div>
                          <div class="col-auto row items-center" style="margin-left: 10px">
                            <q-radio v-model="store.form.is_before_tax_discount" val="% ส่วนลด" label="% ส่วนลด"
                              color="grey-7" size="xs" class="q-mr-sm" />
                            <q-input v-model="store.form.before_tax_discount_percent" dense borderless
                              class="input-container-v3 q-mr-md" style="width: 100px" />
                          </div>

                          <div class="col-auto row items-center" style="margin-left: 10px">
                            <q-radio v-model="store.form.is_before_tax_discount" val="ส่วนลด" label="ส่วนลด"
                              color="grey-7" size="xs" class="q-mr-sm" />
                            <q-input v-model="store.form.before_tax_discount_amount" dense borderless
                              class="input-container-v3 q-mr-md" style="width: 100px; margin-left: 20px" />
                          </div>
                        </div>
                      </div>
                    </q-slide-transition>
                  </div>
                  <div class="col-12 row justify-center" style="margin-top: 20px">
                    <q-checkbox class="mini-container-header" v-model="store.form.is_manual_discount_after_tax"
                      label="ส่วนลดท้ายบิล หลังภาษี" color="blue-grey-9" size="xs" />
                    <q-slide-transition>
                      <div v-if="store.form.is_manual_discount_after_tax" class="mini-container">
                        <!-- รายละเอียดภาษี -->
                        <div class="row q-mb-sm" style="font-size: 13px; margin-top: 10px">
                          <q-radio v-model="store.form.is_after_tax_discount" val="% ส่วนลด" label="% ส่วนลด"
                            color="grey-7" size="xs" style="margin-right: 10px; margin-left: 10px" />

                          <q-input v-model="store.form.after_tax_discount_percent" dense borderless
                            class="input-container-v3" />
                          <q-radio v-model="store.form.is_after_tax_discount" val="ส่วนลด" label="ส่วนลด" color="grey-7"
                            size="xs" style="margin-right: 25px; margin-left: 10px" />

                          <q-input v-model="store.form.after_tax_discount_amount" dense borderless
                            class="input-container-v3" />
                        </div>
                      </div>
                    </q-slide-transition>
                  </div>
                  <div class="col-12 row justify-center" style="margin-top: 10px">
                    <div class="q-mt-sm q-pr-md text-center mini-container-header" style="padding: 5px">
                      การคำนวณราคาทุน
                    </div>
                    <div class="col-12 col-md-8 mini-container">
                      <q-radio v-model="store.form.is_discount_applied" val=false label="ใช้ส่วนลดในการคำนวณทุนสินค้า"
                        color="grey-7" size="xs" />
                      <q-radio v-model="store.form.is_tax_included" val="เพิ่มภาษีลงในราคาทุนสินค้า"
                        label="เพิ่มภาษีลงในราคาทุนสินค้า" color="grey-7" size="xs" hidden />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="gap-container">
            <div class="text-white shadow-2 container-header row items-center">
              รายการสินค้า
              <q-checkbox v-model="store.form.po" label="เลือกจากรายการใบสั่งซื้อ" color="light-blue" size="xs"
                style="margin-left: 10px" />
            </div>
            <div class="shadow-2 container q-pa-md">
              <div class="row items-center q-gutter-sm">
                <!-- Label และ Input -->
                <div class="row items-center">
                  <span v-if="store.form.status === 'เตรียมรายการ'" class="q-mr-sm text-center"
                    style="margin-left: 10px">Barcode</span>
                  <q-input v-if="store.form.status === 'เตรียมรายการ'" v-model="stockStore.selectedBranch" dense
                    borderless class="input-container-2" input-style="padding-bottom: 10px" />
                </div>

                <!-- ปุ่ม 1 -->

                <!-- ปุ่ม 2 -->
                <q-btn v-if="store.form.status === 'เตรียมรายการ'" label="ค้นหาสินค้า" class="btn-add-2" flat dense
                  @click="openProductListDialog()" />
              </div>
              <div class="row items-center q-gutter-sm">
                <q-table flat class="body-table" :rows="store.grDetails" :columns="columns" row-key="id"
                  style="width: 100%; border-radius: 10px; overflow: hidden">
                  <template v-slot:body-cell-actions="props">
                    <q-td :props="props" class="q-gutter-x-sm" style="min-width: 100px">
                      <q-btn v-if="store.form.status === 'เตรียมรายการ'" icon="edit" padding="none" flat
                        style="color: #e19f62" @click="editGRDetails(props.row)" />
                      <q-btn v-if="store.form.status === 'เตรียมรายการ'" icon="delete" padding="none" flat
                        style="color: #b53638" @click="deleteGRDetails(props.row)" />
                    </q-td>
                  </template>
                  <template v-slot:body-cell-index="props">
                    <q-td :props="props">
                      {{ props.rowIndex + 1 }}
                    </q-td>
                  </template>
                  <template v-slot:bottom-row>
                    <q-tr class="total-row">
                      <!-- ให้ "รวม" อยู่ทางขวาโดยใช้ text-right -->
                      <q-td colspan="7" class="text-right total-label justify-end">รวม</q-td>

                      <!-- ให้ตัวเลขอยู่ชิดขวาสุดของตาราง -->
                      <q-td class="text-right total-value">
                        <div class="row justify-end items-center no-wrap">
                          <span class="text-center">{{ store.form.gr_details_total }}</span>
                        </div>
                      </q-td>
                    </q-tr>
                  </template>
                </q-table>
              </div>
            </div>
          </div>

          <div class="gap-container">
            <div class="text-white shadow-2 container-header row items-center">การชำระเงิน</div>
            <div class="shadow-2 container">
              <div class="row items-center q-gutter-sm">
                <!-- ยังไม่ได้ทำ v-model -->
                <span class="q-mr-sm text-center text-payment"
                  style="margin-left: 20px; color: #865b94">ยอดเงินสุทธิ</span>
                <q-input v-model="totalNet" dense borderless class="input-container-3" readonly />
                <span class="q-mr-sm text-center text-payment" style="margin-left: 20px; color: #439e62">ชำระแล้ว</span>
                <q-input v-model="grTotal" dense borderless class="input-container-3" readonly />
                <span class="q-mr-sm text-center text-payment" style="margin-left: 20px; color: #b53638">ค้างชำระ</span>
                <q-input v-model="outstandingPayment" dense borderless class="input-container-3" readonly />
                <span class="q-mr-sm text-center text-payment" style="margin-left: 20px; color: #294888">เงินทอน</span>
                <q-input v-model="cashChange" dense borderless class="input-container-3" readonly />
              </div>
              <div class="row items-center q-gutter-sm">
                <!-- ทำแค่ตารางน้า ข้อมูลอะไร ใส่มั่วเบิ่ด -->
                <q-table flat class="body-table" :rows="paymentStore.payment" :columns="columns2" row-key="id"
                  style="width: 100%; border-radius: 10px; overflow: hidden">
                  <!-- <template v-slot:body-cell-index="props">
                  <q-td :props="props">
                    {{ props.rowIndex + 1 }}
                  </q-td>
                </template> -->
                </q-table>
              </div>
              <div class="row justify-center q-gutter-sm">
                <q-btn v-if="store.form.status === 'ดำเนินการ'" class="btn-jaaitang" dense flat label="ชำระเงิน"
                  style="margin-top: 20px" @click="AddPM" />
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="col-lg-12 row justify-center items-center" style="min-height: 200px;">
            <div class="text-h6 text-grey-6">กำลังโหลดข้อมูล...</div>
          </div>
        </template>
      </q-card-section>
      <q-card-actions align="center">
        <q-btn class="btn-accept" dense flat label="บันทึก" @click="saveDialog" />
        <q-btn class="btn-cancel" dense flat label="ยกเลิก" @click="closeDialog" />
      </q-card-actions>
      <q-card-actions align="right">
        <q-btn class="btn-print" dense flat label="พิมพ์เอกสาร(ย่อ)" @click="closeDialog" />
        <q-btn class="btn-print" dense flat label="พิมพ์เอกสาร(เต็ม)" @click="closeDialog" />
      </q-card-actions>
    </q-card>
  </q-dialog>
  <ReceivePDDialog v-model="receiveProductOpen" :gr-details="selectedGRDetails" :mode="receiveProductMode">
  </ReceivePDDialog>
  <ProductListDialog v-model="productListDialogOpen" :gr-details="store.grDetails"></ProductListDialog>
  <PaymentDialog v-model="paymentDialogOpen" :mode="paymentMode"></PaymentDialog>
  <SearchByDistributor v-model="changeDistributorDialogOpen" :mode="changeDistributorMode"></SearchByDistributor>
</template>

<script setup lang="ts">

import type { QTableColumn } from 'quasar'
import { date } from 'quasar'
import { useGoodsReceiptStore } from 'src/stores/goodsreceipt'
import { computed, ref, watch } from 'vue'
import PaymentDialog from './PaymentDialog.vue'
import ReceivePDDialog from './ReceivePDDialog.vue'
import type { GoodsReceiptDetail } from 'src/types/goodsReceiptDatail'
import { useStockStore } from 'src/stores/stock'
import { useGoodsReceiptPaymentStore } from 'src/stores/goodsReceiptPayment'
import ProductListDialog from './ProductListDialog.vue'
import SearchByDistributor from './SearchByDistributor.vue'
interface Props {
  modelValue: boolean
  mode?: string
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'add'
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'item-updated': []
}>()

const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})
watch(
  () => props.modelValue,
  async (newValue) => {
    if (newValue) {
      try {
        await store.fetchAllGoodsReceipt()
        //initialize to grDetail in table
        // Ensure gr_details is an array before assigning
        if (store.form && Array.isArray(store.form.gr_details)) {
          store.grDetails = store.form.gr_details
        } else {
          // Initialize as empty array if not available
          store.grDetails = []
          if (store.form) {
            store.form.gr_details = []
          }
        }
      } catch (error) {
        console.error('Error initializing GRDetailsforDistribu:', error)
        // Initialize with empty arrays to prevent rendering errors
        store.grDetails = []
        if (store.form) {
          store.form.gr_details = []
        }
      }
    }
  }
)
const store = useGoodsReceiptStore()
const stockStore = useStockStore()
const paymentStore = useGoodsReceiptPaymentStore()
const receiveProductOpen = ref(false)
const receiveProductMode = ref('add')
const productListDialogOpen = ref(false)
const paymentDialogOpen = ref(false)
const paymentMode = ref('add')
const changeDistributorDialogOpen = ref(false)
const changeDistributorMode = ref('change')
const selectedGRDetails = ref<GoodsReceiptDetail | null>(null)
// Use reactive refs instead of computed for editable financial fields
const tax = ref(0)
const totalNet = ref(0)
const grTotal = ref(0)

// Watch for changes in gr_details_total to recalculate financial values
watch(
  () => store.form.gr_details_total,
  (newTotal) => {
    if (typeof newTotal === 'number') {
      // Calculate tax (7% of gr_details_total)
      const calculatedTax = Math.round((newTotal * 7 / 100) * 10000) / 10000
      tax.value = calculatedTax

      // Calculate total net (gr_details_total + tax)
      totalNet.value = Math.round((newTotal + calculatedTax) * 10000) / 10000
    }
  },
  { immediate: true }
)

// Watch for changes in payment to update grTotal
watch(
  () => store.form.payment,
  (newPayment) => {
    if (Array.isArray(newPayment) && newPayment.length > 0) {
      grTotal.value = newPayment.reduce((acc, item) => acc + item.paid_amount, 0)
    } else {
      grTotal.value = 0
    }
  },
  { deep: true, immediate: true }
)

// Watch for changes in store.form to sync financial values
watch(
  () => [store.form.tax, store.form.tax_total, store.form.gr_total],
  ([newTax, newTaxTotal, newGrTotal]) => {
    if (newTax !== undefined && newTax !== tax.value) {
      tax.value = newTax
    }
    if (newTaxTotal !== undefined && newTaxTotal !== totalNet.value) {
      totalNet.value = newTaxTotal
    }
    if (newGrTotal !== undefined && newGrTotal !== grTotal.value) {
      grTotal.value = newGrTotal
    }
  },
  { immediate: true }
)

// Watch for changes in credit_date or credit_days to calculate credit_due_date
watch(
  () => [store.form.credit_date, store.form.credit_days],
  ([newCreditDate, newCreditDays]) => {
    if (newCreditDate && newCreditDays && typeof newCreditDays === 'number') {
      const creditDate = new Date(newCreditDate);
      const dueDate = new Date(creditDate);
      dueDate.setDate(creditDate.getDate() + newCreditDays);
      store.form.credit_due_date = dueDate;
    }
  },
  { immediate: true }
)

// Computed property for cash change calculation
const cashChange = computed(() => {
  if (grTotal.value < totalNet.value) {
    return 0.00;
  }
  return Math.round((grTotal.value - totalNet.value) * 10000) / 10000
})

// Computed property for outstanding payment calculation
const outstandingPayment = computed(() => { //ค้างชำระ
  const outstanding = totalNet.value - grTotal.value
  return outstanding > 0 ? Math.round(outstanding * 10000) / 10000 : 0
})

const closeDialog = async () => {
  await store.fetchGoodsReceiptByStatus()
  isOpen.value = false
  store.resetForm()
}

function AddPM() {
  paymentDialogOpen.value = true
  paymentMode.value = 'add'
}

const openProductListDialog = async () => {
  await stockStore.fetchAllStockByFilterDialog()
  productListDialogOpen.value = true
}
const openChangeDistributorDialog = () => {
  changeDistributorDialogOpen.value = true
  changeDistributorMode.value = 'change'
}
async function editGRDetails(row: GoodsReceiptDetail) {
  await stockStore.fetchStockByProductId(row.product.id)
  await store.fetchDetailsByProductId(row.product.id, store.form.branch.id) //lot number before
  store.formGoodsReceiptDetail = Object.assign({}, store.formGoodsReceiptDetail, row);
  selectedGRDetails.value = row;
  receiveProductOpen.value = true
  receiveProductMode.value = 'edit'
}
function deleteGRDetails(row: GoodsReceiptDetail) {
  store.formGoodsReceiptDetail = Object.assign({}, store.formGoodsReceiptDetail, row);
  const index = store.grDetails.findIndex(item => item.product.id === store.formGoodsReceiptDetail.product.id);
  if (index !== -1) {
    store.grDetails.splice(index, 1);
  }
  store.form.gr_details_total -= row.total_price_product
  console.log(row)
  store.resetFormGRDetails()
}
const saveDialog = async () => {
  store.form.gr_total = grTotal.value //รวมชำระ
  store.form.gr_details = store.grDetails
  store.form.tax = tax.value
  store.form.tax_total = totalNet.value
  if (store.form.status === 'เสร็จสมบูรณ์') {
    store.form.receive_date = new Date()
  }
  await store.updateGr(store.form)
  await store.addProduct(store.grDetails, store.form.id)
  isOpen.value = false
  await store.fetchGoodsReceiptByStatus()
  store.resetForm()
  store.resetFormGRDetails()
}

const formattedDate = computed({
  get() {
    if (!store.form || !store.form.date_document) {
      return '';
    }
    return date.formatDate(store.form.date_document, 'DD/MM/YYYY') // แปลงจาก Date เป็น string ในรูปแบบ YYYY-MM-DD
  },
  set(value: string) {
    if (!store.form) return;
    if (store.form.status == 'เตรียมรายการ') {
      store.form.date_document = new Date('');
    } else if (store.form.status == 'ดำเนินการ') {
      const newDate = new Date(value) // แปลงจาก string กลับเป็น Date
      store.form.date_document = newDate // อัพเดทค่าใน store
    }
  },
})
const formattedCreditDueDate = computed({
  get() {
    if (!store.form || !store.form.credit_due_date) {
      return '';
    }
    return date.formatDate(store.form.credit_due_date, 'DD/MM/YYYY');
  },
  set() {
    // This is readonly - calculated automatically
  },
})
const formattedInvoiceDate = computed({
  get() {
    if (!store.form || !store.form.tax_invoice_date) {
      return '';
    }
    return date.formatDate(store.form.tax_invoice_date, 'DD/MM/YYYY') // แปลงจาก Date เป็น string ในรูปแบบ YYYY-MM-DD
  },
  set(value: string) {
    if (!store.form) return;
    const parts = value.split('/')
    if (parts.length === 3) {
      const [day, month, year] = parts
      if (day && month && year) {
        const newDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day))
        store.form.tax_invoice_date = newDate // ✅ เก็บเป็น Date ตาม type
      }
    } else {
      store.form.tax_invoice_date = new Date()
    }
  },
})
const formattedCreditDate = computed({
  get() {
    if (!store.form || !store.form.credit_date) {
      return '';
    }
    return date.formatDate(store.form.credit_date, 'DD/MM/YYYY') // แปลงจาก Date เป็น string ในรูปแบบ YYYY-MM-DD
  },
  set(value: string) {
    if (!store.form) return;
    const parts = value.split('/')
    if (parts.length === 3) {
      const [day, month, year] = parts
      if (day && month && year) {
        const newDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day))
        store.form.credit_date = newDate // ✅ เก็บเป็น Date ตาม type
      }
    } else {
      store.form.credit_date = new Date()
    }
  },
})
const formattedReceiveDate = computed({
  get() {
    if (!store.form || !store.form.receive_date) {
      return '';
    }
    return date.formatDate(store.form.receive_date, 'DD/MM/YYYY') // แปลงจาก Date เป็น string ในรูปแบบ YYYY-MM-DD
  },
  set(value: string) {
    if (store.form.status == 'เสร็จสมบูรณ์') {
      const newDate = new Date(value) // แปลงจาก string กลับเป็น Date
      store.form.receive_date = newDate // อัพเดทค่าใน store
    }
  },
})
const columns = <QTableColumn[]>[
  {
    name: 'index',
    label: 'เลขที่',
    field: '',
    align: 'left' as const,
    sortable: false,
  },
  {
    name: 'product.product_name',
    label: 'สินค้า',
    field: (row) => (row.product ? row.product.product_name : ''),
    align: 'left' as const,
    sortable: true,
  },
  // {
  //   name: 'id',
  //   label: 'รายละเอียดการรับ',
  //   field: (row) => row.quantity,
  //   align: 'left' as const,
  //   sortable: true,
  // },
  {
    name: 'total_receive_quantity',
    label: 'จำนวนรับ',
    field: 'total_receive_quantity',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'free_quantity',
    label: 'จำนวนแถม',
    field: 'free_quantity',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'receive_price_before_tax',
    label: 'ราคารับต่อหน่วย',
    field: 'receive_price_before_tax',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'cost_unit',
    label: 'ราคาทุนต่อหน่วย',
    field: 'cost_unit',
    align: 'left' as const,
    sortable: true,
  },
  // {
  //   name: 'id',
  //   label: 'ส่วนลด',
  //   field: (row) => row.total_price.toFixed(2),
  //   align: 'left' as const,
  //   sortable: true,
  // },
  {
    name: 'total_price_product',
    label: 'รวมเงิน',
    field: 'total_price_product',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'actions',
    label: '',
    align: 'center' as const,
    field: 'actions',
    sortable: false,
  },
]
const columns2 = <QTableColumn[]>[
  {
    name: 'code',
    label: 'เลขที่',
    field: 'code',
    align: 'left' as const,
    sortable: false,
  },
  {
    name: 'payment_date',
    label: 'วันที่',
    field: 'payment_date',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'user',
    label: 'ผู้บันทึก',
    field: (row) => row.name,
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'receivedBy',
    label: 'ผู้รับ',
    field: (row) => row.name,
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'paid_amount',
    label: 'รายละเอียดการชำระเงิน',
    field: 'paid_amount',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'total_amount',
    label: 'จำนวนเงิน',
    field: 'total_amount',
    align: 'left' as const,
    sortable: true,
  },
]
</script>
<style scoped>
.gap-container {
  margin-bottom: 20px;
}

.container-header {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 55px;
}

.btn-add {
  background-color: #ffffff;
  color: #000000;
  border-radius: 5px;
  margin-left: 20px;
  font-size: 13px;
}

.container {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.input-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.input-container-2 {
  background-color: white;
  border-radius: 5px;
  width: 250px;
  height: 35px;
  margin-left: 10px;
  font-size: 13px;
  padding-left: 10px;
  padding-right: 10px;
  text-align: center;
}

.input-container-3 {
  background-color: white;
  border-radius: 5px;
  width: 150px;
  height: 35px;
  margin-left: 10px;
  font-size: 15px;
  padding-left: 10px;
  padding-right: 10px;
  text-align: center;
}

.mini-container-header {
  background-color: #83a7d8;
  border-radius: 3px 3px 0 0;
  width: 230px;
  height: 30px;
}

.mini-container {
  background-color: white;
  border-radius: 0 0 3px 3px;
  width: 230px;
}

.width-column {
  width: 300px;
}

.input-container-v3 {
  padding-left: 10px;
  padding-right: 10px;
  margin-top: 5px;
  margin-bottom: 5px;
  margin-left: 5px;
  border-radius: 5px;
  background-color: #d6e4f6;
  width: 100px;
}

.btn-accept {
  background-color: #36b54d;
  margin-right: 10px;
}

.btn-cancel {
  background-color: #9c2c2c;
  margin-right: 10px;
}

.btn-add-2 {
  background-color: #294888;
  color: white;
  border-radius: 3px;
  margin-left: 20px;
  font-size: 13px;
  width: 150px;
}

.btn-jaaitang {
  background-color: #36b54d;
  margin-right: 10px;
  width: 100px;
}

.container-headerhalf {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  width: 350px;
  height: 55px;
}

.containerhalf {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  width: 350px;
  height: 100px;
}

.text-po {
  margin-left: 15px;
  font-size: 30px;
}

.gap-container-left {
  margin-left: 20px;
}

.flex-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 40px;
  /* ปรับระยะห่างระหว่าง radio */
  padding: 10px;
}

.containerhalf2 {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  width: 680px;
  height: 100px;
}

.custom-radio .q-radio__inner--truthy {
  background-color: currentColor !important;
  opacity: 1;
}

.container-headerhalf2 {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  width: 680px;
  height: 55px;
}

.status-text {
  margin-left: 300px;
  margin-right: 30px;
}

.body-table {
  background-color: white;
  margin-top: 20px;
}

:deep(.q-table thead tr) {
  background-color: #83a7d8;
}

.total-row {
  background-color: #83a7d8;
  color: black;
  font-weight: bold;
}

.total-value {
  text-align: right;
  padding-right: 16px;
  /* ปรับระยะห่างให้ดูดี */
}

.total-label {
  text-align: right;
}

.text-payment {
  font-size: 14px;
}
</style>